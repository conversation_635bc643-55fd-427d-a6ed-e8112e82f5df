package threeg

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

// SerializableCookie represents a cookie that can be stored in Redis
type SerializableCookie struct {
	Name     string    `json:"name"`
	Domain   string    `json:"domain"`
	Path     string    `json:"path"`
	Secure   bool      `json:"secure"`
	HTTPOnly bool      `json:"httpOnly"`
	Expires  time.Time `json:"expires"`
	ValueEnc []byte    `json:"valueEnc"` // Encrypted value
}

const (
	minTTL     = 5 * time.Minute
	maxTTL     = 2 * time.Hour
	defaultTTL = 30 * time.Minute
	ttlBuffer  = 2 * time.Minute
)

// buildCookieCacheKey returns the Redis key for storing 3G TMS session cookies
func buildCookieCacheKey(integration models.Integration) string {
	return BuildTMSCookieCacheKey(integration.ServiceID, integration.ID, integration.Username)
}

// BuildTMSCookieCacheKey returns the Redis key for storing 3G TMS session cookies
// This is exported so it can be used by other integrations (e.g., Pricing) to reference the same cache
func BuildTMSCookieCacheKey(serviceID uint, integrationID uint, username string) string {
	return fmt.Sprintf("threeg:cookies:%d:%d:%s", serviceID, integrationID, username)
}

// rehydrateCookies reconstructs http.Cookie objects from serialized form and sets them in the jar
func rehydrateCookies(ctx context.Context, jar http.CookieJar, u *url.URL, serialized []SerializableCookie) error {
	return RehydrateTMSCookies(ctx, jar, u, serialized)
}

// RehydrateTMSCookies reconstructs http.Cookie objects from serialized form and sets them in the jar
// This is exported so it can be used by other integrations (e.g., Pricing) to rehydrate TMS cookies
func RehydrateTMSCookies(ctx context.Context, jar http.CookieJar, u *url.URL, serialized []SerializableCookie) error {
	if len(serialized) == 0 {
		return nil
	}

	cookies := make([]*http.Cookie, 0, len(serialized))
	for _, sc := range serialized {
		// Decrypt the cookie value
		decryptedValue, err := crypto.DecryptAESGCM(ctx, string(sc.ValueEnc), nil)
		if err != nil {
			return fmt.Errorf("failed to decrypt cookie %s: %w", sc.Name, err)
		}

		// Fix empty domain/path for old cookies (stored before fix)
		// Empty domain/path prevents cookie jar from matching them correctly
		domain := sc.Domain
		path := sc.Path
		if domain == "" {
			domain = u.Hostname()
		}
		if path == "" {
			path = "/"
		}

		cookies = append(cookies, &http.Cookie{
			Name:     sc.Name,
			Value:    decryptedValue,
			Domain:   domain,
			Path:     path,
			Secure:   sc.Secure,
			HttpOnly: sc.HTTPOnly,
			Expires:  sc.Expires,
		})
	}

	jar.SetCookies(u, cookies)

	log.Info(
		ctx,
		"rehydrated 3G TMS cookies into jar",
		zap.String("url", u.Host),
		zap.Int("count", len(cookies)),
		zap.Any("cookieNames", cookieNames(cookies)),
	)

	return nil
}

// computeTTL calculates the cache TTL based on cookie expirations
func computeTTL(cookies []*http.Cookie) time.Duration {
	if len(cookies) == 0 {
		return defaultTTL
	}

	var minExpires time.Time
	hasExpires := false

	for _, cookie := range cookies {
		if !cookie.Expires.IsZero() {
			if !hasExpires || cookie.Expires.Before(minExpires) {
				minExpires = cookie.Expires
				hasExpires = true
			}
		}
	}

	if !hasExpires {
		return defaultTTL
	}

	// Calculate TTL with buffer
	ttl := time.Until(minExpires) - ttlBuffer

	// If cookie is already expired or will expire very soon, use minimum TTL
	// This prevents negative TTLs and ensures we don't cache expired cookies for too long
	if ttl <= 0 {
		return minTTL
	}

	// Bound to [minTTL, maxTTL]
	if ttl < minTTL {
		return minTTL
	}
	if ttl > maxTTL {
		return maxTTL
	}

	return ttl
}

// loadCookiesFromRedis retrieves and deserializes cookies from Redis
func loadCookiesFromRedis(ctx context.Context, key string) ([]SerializableCookie, bool, error) {
	return LoadTMSCookiesFromRedis(ctx, key)
}

// LoadTMSCookiesFromRedis retrieves and deserializes cookies from Redis
// This is exported so it can be used by other integrations (e.g., Pricing) to load TMS cookies
func LoadTMSCookiesFromRedis(ctx context.Context, key string) ([]SerializableCookie, bool, error) {
	cookies, found, err := redis.GetKey[[]SerializableCookie](ctx, key)
	if err != nil {
		// Treat missing keys as not found, not an error
		if errors.Is(err, redis.NilEntry) {
			return nil, false, nil
		}
		return nil, false, fmt.Errorf("failed to get cookies from redis: %w", err)
	}

	if !found {
		return nil, false, nil
	}

	return cookies, true, nil
}

// saveCookiesToRedis serializes and stores cookies in Redis with TTL
func saveCookiesToRedis(ctx context.Context, key string, cookies []SerializableCookie, ttl time.Duration) error {
	if len(cookies) == 0 {
		return nil
	}

	if err := redis.SetKey(ctx, key, cookies, ttl); err != nil {
		return fmt.Errorf("failed to save cookies to redis: %w", err)
	}

	log.Info(
		ctx,
		"saved 3G TMS cookies to redis",
		zap.String("key", key),
		zap.Int("count", len(cookies)),
		zap.Any("cookieNames", serialCookieNames(cookies)),
		zap.Duration("ttl", ttl),
	)

	return nil
}

// deleteCookiesFromRedis removes cached cookies from Redis
func deleteCookiesFromRedis(ctx context.Context, key string) error {
	err := redis.DeleteKey(ctx, key)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to delete 3G TMS cookies from redis", zap.String("key", key), zap.Error(err))
		return err
	}

	if err == nil {
		log.Info(ctx, "deleted cached 3G TMS session cookies")
	} else { // err is redis.NilEntry
		log.Debug(ctx, "3G TMS session cookies not found in cache (already cleared)")
	}
	return nil
}

// cookieNames returns only the names for logging purposes
func cookieNames(cs []*http.Cookie) []string {
	out := make([]string, 0, len(cs))
	for _, c := range cs {
		out = append(out, c.Name)
	}
	return out
}

// serialCookieNames returns only the names from SerializableCookie for logging
func serialCookieNames(cs []SerializableCookie) []string {
	out := make([]string, 0, len(cs))
	for _, c := range cs {
		out = append(out, c.Name)
	}
	return out
}
