package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

func (c *Client) GetCustomerAccessorials(_ context.Context, _, _, _ int) ([]models.Accessorial, error) {
	return nil, helpers.NotImplemented(models.ThreeG, "GetCustomerAccessorials")
}

func (c *Client) GetStaticCustomerAccessorials() []models.Accessorial {
	// Not implemented
	return nil
}
