package threeg

import (
	"github.com/drumkitai/drumkit/common/models"
)

type (
	SaveLTLQuickQuoteInputRequest struct {
		Request                LTLQuoteRequest
		CustomerOrganizationID int
		CustomerName           string
		CustomerStaffName      string
		BrokerOrganizationName string
		Pickup                 models.Stop
		Dropoff                models.Stop
		EncryptedQuotes        string
	}

	SaveLTLQuickQuoteResult struct {
		QuoteURL    string
		QuoteID     int
		QuoteSource models.QuoteSource
		QuoteLabel  string
	}
)
