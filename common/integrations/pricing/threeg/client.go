package threeg

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	API interface {
		SearchCustomers(ctx context.Context, query string) ([]models.TMSCustomer, error)
		SearchCustomerStaff(
			ctx context.Context,
			customerOrganizationID int,
			customerOrganizationName string,
			enabled bool,
		) ([]models.TMSCustomer, error)

		GetCustomerAccessorials(
			ctx context.Context,
			customerOrgID,
			brokerID,
			shipmentType int,
		) ([]models.Accessorial, error)
		GetStaticCustomerAccessorials() []models.Accessorial

		GetMileageByZipCodes(
			ctx context.Context,
			customerOrganizationID int,
			originZip,
			originCountry,
			destinationZip,
			destinationCountry string,
			pcmilerRoutingType int,
		) (float64, error)

		GetLTLQuickQuotes(
			ctx context.Context,
			req LTLQuoteRequest,
		) ([]models.QuickQuote, error)

		GetStaticServiceLevels() []string
	}

	Client struct {
		Integration models.Integration `json:"-"`
		UserName    string             `json:"userName"`
		Cookies     []*http.Cookie     `json:"cookies"`
		HTTPClient  *http.Client       `json:"-"`
		WebBaseURL  string             `json:"webBaseURL"`
	}
)

func New(ctx context.Context, integration models.Integration) (*Client, error) {
	username := integration.Username
	password := integration.EncryptedPassword

	cachedClient := getCachedClient(ctx, username)

	if cachedClient != nil {
		cachedClient.Integration = integration
		return cachedClient, nil
	}

	hostname := getHostname(integration.Tenant)
	webBaseURL := fmt.Sprintf("https://%s", hostname)

	if username == "" || password == nil {
		return nil, errors.New("missing ThreeG username or password")
	}

	client := &Client{
		UserName:    username,
		Integration: integration,
		WebBaseURL:  webBaseURL,
	}

	if err := client.login(ctx); err != nil {
		return nil, fmt.Errorf("error authenticating with 3G TMS: %w", err)
	}

	err := client.cache(ctx)
	if err != nil {
		log.Warn(ctx, "failed to set ThreeG client in Redis", zap.Error(err))
	}

	return client, nil
}
