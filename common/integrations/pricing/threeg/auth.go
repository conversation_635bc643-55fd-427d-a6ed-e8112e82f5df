package threeg

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	tmsthreg "github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

const loginEndpoint = "/web/resources/j_spring_security_check"

// ensureHTTPClient guarantees an http.Client with cookie jar.
func (c *Client) ensureHTTPClient() error {
	if c.HTTPClient != nil && c.HTTPClient.Jar != nil {
		return nil
	}

	jar, err := cookiejar.New(&cookiejar.Options{
		PublicSuffixList: publicsuffix.List,
	})
	if err != nil {
		return fmt.Errorf("failed to create cookie jar: %w", err)
	}

	client := otel.TracingHTTPClient(60 * time.Second)
	client.Jar = jar

	c.HTTPClient = client

	return nil
}

func getHostname(tenant string) string {
	// Strip http:// or https:// if present (in case user pastes full URL)
	hostname := strings.TrimPrefix(tenant, "https://")
	hostname = strings.TrimPrefix(hostname, "http://")

	if strings.Contains(hostname, ".") {
		// Tenant is already a full hostname
		return hostname
	}
	// Legacy fallback: append .3gtms.com for backward compatibility
	// Note: This assumes all tenants use the 3gtms.com domain, which may not be true for all customers.
	// For custom domains, provide the full hostname in the tenant field.
	return fmt.Sprintf("%s.3gtms.com", hostname)
}

// login performs authentication with 3G TMS and returns the session cookie
func (c *Client) login(ctx context.Context) error {
	// Use the web base URL from the ThreeG struct (handles custom hostnames)
	endpoint := fmt.Sprintf("%s%s", c.WebBaseURL, loginEndpoint)

	// Decrypt the password
	decryptedPassword, err := crypto.DecryptAESGCM(ctx, string(c.Integration.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("error decrypting password: %w", err)
	}

	if err := c.ensureHTTPClient(); err != nil {
		return err
	}

	log.Debug(ctx, "ThreeG auth: HTTP client ready with cookie jar")
	// Create form data for login
	formData := url.Values{}
	formData.Set("j_username", c.Integration.Username)
	formData.Set("j_password", decryptedPassword)

	// Create the request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return fmt.Errorf("error creating login request: %w", err)
	}

	// Set headers to match the successful curl request
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	//nolint:lll
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("DNT", "1")
	req.Header.Set("Origin", c.WebBaseURL)
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Referer", fmt.Sprintf("%s/web/login", c.WebBaseURL))
	req.Header.Set("Sec-Ch-Ua", `"Chromium";v="137", "Not/A)Brand";v="24"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"macOS"`)
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	//nolint:lll
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36")

	// Make the request
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error making login request: %w", err)
	}
	defer resp.Body.Close()

	// Check for successful login
	if resp.StatusCode != http.StatusOK {
		// Read response body for error details
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("login failed with status: %s (could not read response body)", resp.Status)
		}
		return fmt.Errorf("login failed with status: %s - response: %s", resp.Status, string(body))
	}

	// Verify we got a session cookie
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		return fmt.Errorf("error parsing URL: %w", err)
	}

	cookies := c.HTTPClient.Jar.Cookies(parsedURL)
	if len(cookies) == 0 {
		return errors.New("no session cookie received after login")
	}

	requiredCookie := "JSESSIONID" // AWSALB and AWSALBCORS are optional
	found := false
	for _, cookie := range cookies {
		if cookie.Name == requiredCookie {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("missing required cookie after login: %s", requiredCookie)
	}

	return nil
}

// loginWithTMSCache performs authentication using the shared TMS cache
// It first tries to load cookies from the TMS cache, and if not found, performs login and saves to TMS cache
func (c *Client) loginWithTMSCache(ctx context.Context, tmsCacheKey string) error {
	if err := c.ensureHTTPClient(); err != nil {
		return err
	}

	// Try to load cookies from TMS cache first
	cachedCookies, found, err := tmsthreg.LoadTMSCookiesFromRedis(ctx, tmsCacheKey)
	if err != nil {
		log.Warn(ctx, "failed to load TMS cookies for pricing", zap.String("tmsCacheKey", tmsCacheKey), zap.Error(err))
	} else if found && len(cachedCookies) > 0 {
		// Rehydrate cookies into the jar
		parsedURL, err := url.Parse(c.WebBaseURL)
		if err != nil {
			log.Warn(ctx, "failed to parse web base URL for cookie rehydration", zap.Error(err))
		} else {
			if err := tmsthreg.RehydrateTMSCookies(ctx, c.HTTPClient.Jar, parsedURL, cachedCookies); err != nil {
				log.Warn(ctx, "failed to rehydrate TMS cookies for pricing", zap.Error(err))
			} else {
				// Verify we have the required session cookie
				cookies := c.HTTPClient.Jar.Cookies(parsedURL)
				for _, cookie := range cookies {
					if cookie.Name == "JSESSIONID" {
						log.Info(ctx, "reusing TMS authentication for pricing", zap.String("tmsCacheKey", tmsCacheKey))
						return nil
					}
				}
				log.Warn(ctx, "no JSESSIONID cookie found in cached TMS cookies")
			}
		}
	}

	// If we reach here, we need to perform a fresh login
	log.Info(ctx, "performing fresh login for pricing (will save to TMS cache)", zap.String("tmsCacheKey", tmsCacheKey))

	if err := c.login(ctx); err != nil {
		return err
	}

	// Save cookies to TMS cache for reuse by TMS integration
	if err := c.saveCookiesToTMSCache(ctx, tmsCacheKey); err != nil {
		log.Warn(ctx, "failed to save cookies to TMS cache", zap.String("tmsCacheKey", tmsCacheKey), zap.Error(err))
		// Don't fail the login if we can't cache - the login itself succeeded
	}

	return nil
}

// saveCookiesToTMSCache saves the current session cookies to the TMS cache
func (c *Client) saveCookiesToTMSCache(ctx context.Context, tmsCacheKey string) error {
	parsedURL, err := url.Parse(c.WebBaseURL)
	if err != nil {
		return fmt.Errorf("error parsing web base URL: %w", err)
	}

	jarCookies := c.HTTPClient.Jar.Cookies(parsedURL)
	if len(jarCookies) == 0 {
		return errors.New("no cookies to save")
	}

	// Convert cookies to serializable format (same as TMS integration)
	serialized := make([]tmsthreg.SerializableCookie, 0, len(jarCookies))
	for _, cookie := range jarCookies {
		// Encrypt the cookie value
		encryptedValue, err := crypto.EncryptAESGCM(ctx, cookie.Value, nil)
		if err != nil {
			log.Warn(ctx, "failed to encrypt cookie for TMS cache", zap.String("name", cookie.Name), zap.Error(err))
			continue
		}

		// Set cookie attributes based on URL
		domain := parsedURL.Hostname()
		path := "/"
		secure := parsedURL.Scheme == "https"
		httpOnly := true // Session cookies should be httpOnly

		serialized = append(serialized, tmsthreg.SerializableCookie{
			Name:     cookie.Name,
			Domain:   domain,
			Path:     path,
			Secure:   secure,
			HTTPOnly: httpOnly,
			Expires:  cookie.Expires,
			ValueEnc: []byte(encryptedValue),
		})
	}

	if len(serialized) == 0 {
		return errors.New("no cookies could be serialized")
	}

	// Calculate TTL based on cookie expiration (same logic as TMS)
	ttl := computeTTL(jarCookies)

	// Save to Redis using the TMS cache key
	if err := saveCookiesToRedis(ctx, tmsCacheKey, serialized, ttl); err != nil {
		return fmt.Errorf("failed to save cookies to TMS cache: %w", err)
	}

	log.Info(ctx, "saved pricing cookies to TMS cache",
		zap.String("tmsCacheKey", tmsCacheKey),
		zap.Int("cookieCount", len(serialized)),
		zap.Duration("ttl", ttl))

	return nil
}

// computeTTL calculates the cache TTL based on cookie expirations (copied from TMS)
func computeTTL(cookies []*http.Cookie) time.Duration {
	const (
		minTTL     = 5 * time.Minute
		maxTTL     = 2 * time.Hour
		defaultTTL = 30 * time.Minute
		ttlBuffer  = 2 * time.Minute
	)

	if len(cookies) == 0 {
		return defaultTTL
	}

	var minExpires time.Time
	hasExpires := false

	for _, cookie := range cookies {
		if !cookie.Expires.IsZero() {
			if !hasExpires || cookie.Expires.Before(minExpires) {
				minExpires = cookie.Expires
				hasExpires = true
			}
		}
	}

	if !hasExpires {
		return defaultTTL
	}

	// Calculate TTL with buffer
	ttl := time.Until(minExpires) - ttlBuffer

	// If cookie is already expired or will expire very soon, use minimum TTL
	if ttl <= 0 {
		return minTTL
	}

	// Bound to [minTTL, maxTTL]
	if ttl < minTTL {
		return minTTL
	}
	if ttl > maxTTL {
		return maxTTL
	}

	return ttl
}

// saveCookiesToRedis saves serialized cookies to Redis (copied from TMS)
func saveCookiesToRedis(ctx context.Context, key string, cookies []tmsthreg.SerializableCookie, ttl time.Duration) error {
	if len(cookies) == 0 {
		return nil
	}

	if err := redis.SetKey(ctx, key, cookies, ttl); err != nil {
		return fmt.Errorf("failed to save cookies to redis: %w", err)
	}

	return nil
}
