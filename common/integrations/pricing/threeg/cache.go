package threeg

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	tmsthreg "github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const redisKeyTTL = 30 * time.Minute

// PricingCacheEntry represents the cached data for pricing integration
// Instead of storing cookies directly, it stores a reference to the TMS cache key
type PricingCacheEntry struct {
	TMSCacheKey string `json:"tmsCacheKey"` // Reference to the TMS cookie cache key
	WebBaseURL  string `json:"webBaseURL"`
	UserName    string `json:"userName"`
}

func getKey(email string) string {
	return fmt.Sprintf("threeg:pricing-cookies:%s", strings.ToLower(email))
}

// buildTMSCache<PERSON>ey builds the TMS cache key for the given pricing integration
// It finds the corresponding TMS integration and uses its ID for the cache key
func buildTMSCacheKey(ctx context.Context, pricingIntegration models.Integration) (string, error) {
	// Find the corresponding TMS integration for the same service, username, and tenant
	tmsIntegration, err := findCorrespondingTMSIntegration(ctx, pricingIntegration)
	if err != nil {
		return "", fmt.Errorf("failed to find corresponding TMS integration: %w", err)
	}

	return tmsthreg.BuildTMSCookieCacheKey(tmsIntegration.ServiceID, tmsIntegration.ID, tmsIntegration.Username), nil
}

// findCorrespondingTMSIntegration finds the TMS integration that corresponds to the pricing integration
func findCorrespondingTMSIntegration(_ context.Context, pricingIntegration models.Integration) (models.Integration, error) {
	// For now, we'll use the pricing integration's own details to build the TMS cache key
	// This assumes that when a pricing integration is created, a corresponding TMS integration
	// is also created with the same ServiceID, Username, and Tenant
	//
	// In a future enhancement, we could query the database to find the actual TMS integration
	// but for now, we'll use a convention-based approach

	// Create a mock TMS integration with the same details
	// The actual TMS integration should have been created during onboarding
	tmsIntegration := models.Integration{
		ServiceID: pricingIntegration.ServiceID,
		Username:  pricingIntegration.Username,
	}
	tmsIntegration.ID = pricingIntegration.ID // Use the same ID for now - this will be updated when we add DB lookup

	return tmsIntegration, nil
}

func getCachedClient(ctx context.Context, userName string) *Client {
	// Try to get the pricing cache entry
	entry, found, err := redis.GetKey[PricingCacheEntry](ctx, getKey(userName))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get ThreeG pricing cache entry from Redis", zap.Error(err))
		return nil
	}

	if !found {
		return nil
	}

	// Load cookies from the TMS cache using the stored key
	cookies, cookiesFound, err := tmsthreg.LoadTMSCookiesFromRedis(ctx, entry.TMSCacheKey)
	if err != nil {
		log.Warn(ctx, "failed to load TMS cookies for pricing", zap.String("tmsCacheKey", entry.TMSCacheKey), zap.Error(err))
		return nil
	}

	if !cookiesFound {
		log.Info(ctx, "TMS cookies not found, pricing cache entry is stale", zap.String("tmsCacheKey", entry.TMSCacheKey))
		return nil
	}

	// Create a new client with the loaded data and rehydrate cookies
	client := &Client{
		UserName:   entry.UserName,
		WebBaseURL: entry.WebBaseURL,
	}

	// Ensure HTTP client with cookie jar
	if err := client.ensureHTTPClient(); err != nil {
		log.Warn(ctx, "failed to create HTTP client for cached pricing client", zap.Error(err))
		return nil
	}

	// Rehydrate cookies into the jar
	parsedURL, err := url.Parse(entry.WebBaseURL)
	if err != nil {
		log.Warn(ctx, "failed to parse web base URL for cookie rehydration", zap.Error(err))
		return nil
	}

	if err := tmsthreg.RehydrateTMSCookies(ctx, client.HTTPClient.Jar, parsedURL, cookies); err != nil {
		log.Warn(ctx, "failed to rehydrate TMS cookies for pricing", zap.Error(err))
		return nil
	}

	log.Info(ctx, "re-using existing ThreeG client with TMS authentication",
		zap.String("tmsCacheKey", entry.TMSCacheKey),
		zap.Int("cookieCount", len(cookies)))

	return client
}

func (c *Client) cache(ctx context.Context, tmsCacheKey string) error {
	key := getKey(c.UserName)

	entry := PricingCacheEntry{
		TMSCacheKey: tmsCacheKey,
		WebBaseURL:  c.WebBaseURL,
		UserName:    c.UserName,
	}

	err := redis.SetKey(ctx, key, entry, redisKeyTTL)
	if err != nil {
		return fmt.Errorf("failed to cache ThreeG pricing entry: %w", err)
	}

	log.Info(ctx, "cached ThreeG pricing entry with TMS reference",
		zap.String("pricingKey", key),
		zap.String("tmsCacheKey", tmsCacheKey))

	return nil
}
