package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/globaltranztms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

type (
	// RefreshLocationsGlobalTranzRequest represents a request to refresh
	// GlobalTranz locations for a specific customer using the upstream
	// SearchAddresses API.
	//
	// This is used by the Alexandria FE to ensure the location dropdown has
	// access to newly created GTZ locations that may not be included in the
	// original address book batch (due to upstream limits).
	RefreshLocationsGlobalTranzRequest struct {
		TMSID      uint   `json:"tmsID"`
		CustomerBK string `json:"customerBK"`
		Name       string `json:"name"`
	}

	// RefreshLocationsGlobalTranzResponse is a lightweight response that
	// reports a user-friendly message and how many locations were processed.
	RefreshLocationsGlobalTranzResponse struct {
		Message      string `json:"message"`
		CreatedCount int    `json:"createdCount"`
	}
)

// RefreshLocationsGlobalTranz calls the GlobalTranz SearchAddresses API for a
// specific customer + keyword, upserts any returned locations into our DB, and
// returns a summary. This endpoint is GTZ-only and is designed to be used
// alongside GET /locations?tmsID=...&customerID=... so that refreshed
// locations appear in the standard location list.
func RefreshLocationsGlobalTranz(c *fiber.Ctx) error {
	var body RefreshLocationsGlobalTranzRequest
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(
		c.UserContext(),
		zap.Uint("tmsID", body.TMSID),
		zap.String("customerBK", body.CustomerBK),
		zap.String("name", body.Name),
	)
	userServiceID := middleware.ServiceIDFromContext(c)

	if body.CustomerBK == "" || body.Name == "" {
		return c.Status(http.StatusBadRequest).JSON(
			RefreshLocationsGlobalTranzResponse{
				Message: "customerBK and name are required",
			},
		)
	}

	tmsIntegration, err := integrationDB.Get(ctx, body.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to refresh locations for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching TMS integration from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.GlobalTranzTMS {
		return c.Status(http.StatusBadRequest).JSON(
			RefreshLocationsGlobalTranzResponse{
				Message: "this endpoint is only available for GlobalTranz TMS",
			},
		)
	}

	// Resolve the TMS customer so we can correctly associate new locations
	// with a specific TMSCustomerID. This ensures subsequent calls to
	// GET /locations?tmsID=...&customerID=... include these refreshed
	// locations.
	customer, err := tmsCustomerDB.GetByExternalTMSID(ctx, tmsIntegration.ID, body.CustomerBK)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"customer BK not found for GlobalTranz integration",
				zap.String("customerBK", body.CustomerBK),
				zap.Uint("tmsID", tmsIntegration.ID),
			)
			return c.Status(http.StatusBadRequest).JSON(
				RefreshLocationsGlobalTranzResponse{
					Message: "customerBK not found for this GlobalTranz integration",
				},
			)
		}

		log.Error(ctx, "error fetching TMS customer by external ID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	gtClient, ok := client.(*globaltranztms.GlobalTranz)
	if !ok {
		log.Error(
			ctx,
			"failed to cast client to GlobalTranz",
			zap.String("tmsName", string(tmsIntegration.Name)),
		)
		return c.SendStatus(http.StatusInternalServerError)
	}

	locations, err := gtClient.SearchLocationsByCustomer(ctx, body.CustomerBK, body.Name, &customer.ID)
	if err != nil {
		log.Error(
			ctx,
			"error searching GlobalTranz locations via SearchAddresses",
			zap.Error(err),
		)
		return c.Status(http.StatusInternalServerError).JSON(
			RefreshLocationsGlobalTranzResponse{
				Message: "failed to refresh locations from GlobalTranz",
			},
		)
	}

	if len(locations) == 0 {
		return c.Status(http.StatusBadRequest).JSON(
			RefreshLocationsGlobalTranzResponse{
				Message: "No similar address found for the selected customer. " +
					"Please fill the form to add a new location for this customer.",
			},
		)
	}

	if err := tmsLocationDB.RefreshTMSLocations(ctx, &locations); err != nil {
		log.Error(
			ctx,
			"failed to upsert refreshed GlobalTranz locations into DB",
			zap.Error(err),
			zap.Int("locationCount", len(locations)),
		)
		// Return a 500 so the FE knows this failed, but include a hint in the
		// message for easier debugging.
		return c.Status(http.StatusInternalServerError).JSON(
			RefreshLocationsGlobalTranzResponse{
				Message: "failed to persist refreshed GlobalTranz locations",
			},
		)
	}

	return c.Status(http.StatusOK).JSON(
		RefreshLocationsGlobalTranzResponse{
			Message:      "Successfully refreshed locations from GlobalTranz Address Book.",
			CreatedCount: len(locations),
		},
	)
}
